"""
事件引擎 - 事件发布订阅机制
负责模块间的事件通信，降低耦合度
"""

import logging
import threading
from typing import Dict, List, Callable, Any, Optional
from collections import defaultdict
import traceback
from datetime import datetime


class EventEngine:
    """事件引擎 - 事件发布订阅机制"""
    
    def __init__(self):
        """初始化事件引擎"""
        self.subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self.lock = threading.RLock()
        self.event_history: List[Dict] = []
        self.max_history = 1000
        self.is_initialized = False
        
    def initialize(self) -> bool:
        """初始化事件引擎"""
        try:
            logging.info("📡 正在初始化事件引擎...")
            
            # 注册系统事件
            self._register_system_events()
            
            self.is_initialized = True
            logging.info("✅ 事件引擎初始化完成")
            return True
            
        except Exception as e:
            logging.error(f"❌ 事件引擎初始化失败: {e}")
            return False
    
    def subscribe(self, event_name: str, callback: Callable) -> bool:
        """订阅事件
        
        Args:
            event_name: 事件名称，支持通配符 '*'
            callback: 回调函数，签名为 callback(event_name: str, data: Any)
            
        Returns:
            是否订阅成功
        """
        try:
            with self.lock:
                if callback not in self.subscribers[event_name]:
                    self.subscribers[event_name].append(callback)
                    logging.debug(f"📡 事件订阅成功: {event_name}")
                    return True
                else:
                    logging.warning(f"⚠️ 重复订阅事件: {event_name}")
                    return False
                    
        except Exception as e:
            logging.error(f"❌ 事件订阅失败 {event_name}: {e}")
            return False
    
    def unsubscribe(self, event_name: str, callback: Callable) -> bool:
        """取消订阅事件
        
        Args:
            event_name: 事件名称
            callback: 回调函数
            
        Returns:
            是否取消订阅成功
        """
        try:
            with self.lock:
                if event_name in self.subscribers and callback in self.subscribers[event_name]:
                    self.subscribers[event_name].remove(callback)
                    logging.debug(f"📡 取消事件订阅: {event_name}")
                    return True
                else:
                    logging.warning(f"⚠️ 未找到事件订阅: {event_name}")
                    return False
                    
        except Exception as e:
            logging.error(f"❌ 取消事件订阅失败 {event_name}: {e}")
            return False
    
    def publish(self, event_name: str, data: Any = None) -> bool:
        """发布事件
        
        Args:
            event_name: 事件名称
            data: 事件数据
            
        Returns:
            是否发布成功
        """
        try:
            if not self.is_initialized:
                return False
                
            # 记录事件历史
            self._record_event(event_name, data)
            
            # 获取订阅者
            callbacks = []
            with self.lock:
                # 精确匹配
                if event_name in self.subscribers:
                    callbacks.extend(self.subscribers[event_name])
                
                # 通配符匹配
                if '*' in self.subscribers:
                    callbacks.extend(self.subscribers['*'])
                
                # 模式匹配（如 app.* 匹配 app.started）
                for pattern in self.subscribers:
                    if pattern.endswith('*') and event_name.startswith(pattern[:-1]):
                        callbacks.extend(self.subscribers[pattern])
            
            # 异步调用回调函数
            success_count = 0
            for callback in callbacks:
                try:
                    # 在新线程中执行回调，避免阻塞
                    threading.Thread(
                        target=self._safe_callback,
                        args=(callback, event_name, data),
                        daemon=True
                    ).start()
                    success_count += 1
                except Exception as e:
                    logging.error(f"❌ 事件回调执行失败 {event_name}: {e}")
            
            logging.debug(f"📡 事件发布成功: {event_name} ({success_count} 个订阅者)")
            return True
            
        except Exception as e:
            logging.error(f"❌ 事件发布失败 {event_name}: {e}")
            return False
    
    def _safe_callback(self, callback: Callable, event_name: str, data: Any) -> None:
        """安全执行回调函数"""
        try:
            callback(event_name, data)
        except Exception as e:
            logging.error(f"❌ 事件回调执行异常 {event_name}: {e}")
            traceback.print_exc()
    
    def _record_event(self, event_name: str, data: Any) -> None:
        """记录事件历史"""
        try:
            event_record = {
                'name': event_name,
                'data': data,
                'timestamp': datetime.now().isoformat(),
                'thread_id': threading.current_thread().ident
            }
            
            with self.lock:
                self.event_history.append(event_record)
                
                # 限制历史记录数量
                if len(self.event_history) > self.max_history:
                    self.event_history = self.event_history[-self.max_history:]
                    
        except Exception as e:
            logging.error(f"❌ 记录事件历史失败: {e}")
    
    def get_event_history(self, event_name: Optional[str] = None, limit: int = 100) -> List[Dict]:
        """获取事件历史
        
        Args:
            event_name: 事件名称过滤，如果为None则返回所有事件
            limit: 返回记录数量限制
            
        Returns:
            事件历史记录列表
        """
        try:
            with self.lock:
                history = self.event_history.copy()
            
            # 过滤事件名称
            if event_name:
                history = [event for event in history if event['name'] == event_name]
            
            # 限制数量
            return history[-limit:] if limit > 0 else history
            
        except Exception as e:
            logging.error(f"❌ 获取事件历史失败: {e}")
            return []
    
    def get_subscribers(self, event_name: Optional[str] = None) -> Dict[str, int]:
        """获取订阅者统计
        
        Args:
            event_name: 事件名称，如果为None则返回所有事件的订阅者统计
            
        Returns:
            订阅者统计字典
        """
        try:
            with self.lock:
                if event_name:
                    return {event_name: len(self.subscribers.get(event_name, []))}
                else:
                    return {name: len(callbacks) for name, callbacks in self.subscribers.items()}
                    
        except Exception as e:
            logging.error(f"❌ 获取订阅者统计失败: {e}")
            return {}
    
    def clear_history(self) -> bool:
        """清空事件历史"""
        try:
            with self.lock:
                self.event_history.clear()
            logging.info("🗑️ 事件历史已清空")
            return True
        except Exception as e:
            logging.error(f"❌ 清空事件历史失败: {e}")
            return False
    
    def _register_system_events(self) -> None:
        """注册系统事件"""
        # 应用生命周期事件
        system_events = [
            'app.initialized',
            'app.starting',
            'app.started',
            'app.stopping',
            'app.stopped',
            'config.changed',
            'theme.changed',
            'language.changed',
            'browser.created',
            'browser.started',
            'browser.deleted',
            'plugin.synced',
            'icon.downloaded',
            'update.available',
            'update.downloaded',
            'error.occurred'
        ]
        
        for event in system_events:
            # 预注册事件（创建空的订阅者列表）
            if event not in self.subscribers:
                self.subscribers[event] = []
        
        logging.debug(f"📡 系统事件注册完成: {len(system_events)} 个事件")


# 全局事件引擎实例
event_engine = EventEngine()
