"""
日志引擎 - 统一日志管理
负责日志配置、输出和管理
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional


class LogEngine:
    """日志引擎 - 统一日志管理"""
    
    def __init__(self, app_root: Path):
        """初始化日志引擎"""
        self.app_root = app_root
        self.log_dir = app_root / 'data' / 'logs'
        self.is_initialized = False
        
    def initialize(self, log_level: str = 'INFO', console_output: bool = True) -> bool:
        """初始化日志引擎
        
        Args:
            log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            console_output: 是否输出到控制台
            
        Returns:
            是否初始化成功
        """
        try:
            # 创建日志目录
            self.log_dir.mkdir(parents=True, exist_ok=True)
            
            # 配置根日志记录器
            root_logger = logging.getLogger()
            root_logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
            
            # 清除现有处理器
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # 创建格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            # 文件处理器 - 应用日志
            app_log_file = self.log_dir / f'app_{datetime.now().strftime("%Y%m%d")}.log'
            file_handler = logging.handlers.RotatingFileHandler(
                app_log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            
            # 错误日志文件处理器
            error_log_file = self.log_dir / f'error_{datetime.now().strftime("%Y%m%d")}.log'
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_file,
                maxBytes=5*1024*1024,  # 5MB
                backupCount=3,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(formatter)
            root_logger.addHandler(error_handler)
            
            # 控制台处理器
            if console_output:
                console_handler = logging.StreamHandler(sys.stdout)
                console_handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
                
                # 控制台使用简化格式
                console_formatter = logging.Formatter(
                    '%(levelname)s - %(message)s'
                )
                console_handler.setFormatter(console_formatter)
                root_logger.addHandler(console_handler)
            
            self.is_initialized = True
            logging.info("📝 日志引擎初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 日志引擎初始化失败: {e}")
            return False
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志记录器
        
        Args:
            name: 日志记录器名称
            
        Returns:
            日志记录器实例
        """
        return logging.getLogger(name)
    
    def set_level(self, level: str) -> bool:
        """设置日志级别
        
        Args:
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            
        Returns:
            是否设置成功
        """
        try:
            log_level = getattr(logging, level.upper(), None)
            if log_level is None:
                return False
                
            root_logger = logging.getLogger()
            root_logger.setLevel(log_level)
            
            # 更新控制台处理器级别
            for handler in root_logger.handlers:
                if isinstance(handler, logging.StreamHandler) and handler.stream == sys.stdout:
                    handler.setLevel(log_level)
            
            logging.info(f"📝 日志级别已设置为: {level}")
            return True
            
        except Exception as e:
            logging.error(f"❌ 设置日志级别失败: {e}")
            return False
    
    def cleanup_old_logs(self, days: int = 7) -> bool:
        """清理旧日志文件
        
        Args:
            days: 保留天数
            
        Returns:
            是否清理成功
        """
        try:
            if not self.log_dir.exists():
                return True
            
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days)
            
            deleted_count = 0
            for log_file in self.log_dir.glob('*.log*'):
                try:
                    # 获取文件修改时间
                    file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if file_time < cutoff_date:
                        log_file.unlink()
                        deleted_count += 1
                except Exception as e:
                    logging.warning(f"⚠️ 删除日志文件失败 {log_file}: {e}")
            
            if deleted_count > 0:
                logging.info(f"🗑️ 清理旧日志文件: {deleted_count} 个")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 清理旧日志文件失败: {e}")
            return False
    
    def get_log_files(self) -> list:
        """获取所有日志文件列表
        
        Returns:
            日志文件路径列表
        """
        try:
            if not self.log_dir.exists():
                return []
            
            log_files = []
            for log_file in self.log_dir.glob('*.log*'):
                log_files.append({
                    'path': str(log_file),
                    'name': log_file.name,
                    'size': log_file.stat().st_size,
                    'modified': datetime.fromtimestamp(log_file.stat().st_mtime).isoformat()
                })
            
            # 按修改时间排序
            log_files.sort(key=lambda x: x['modified'], reverse=True)
            return log_files
            
        except Exception as e:
            logging.error(f"❌ 获取日志文件列表失败: {e}")
            return []
    
    def read_log_file(self, file_name: str, lines: int = 100) -> str:
        """读取日志文件内容
        
        Args:
            file_name: 日志文件名
            lines: 读取行数（从末尾开始）
            
        Returns:
            日志文件内容
        """
        try:
            log_file = self.log_dir / file_name
            if not log_file.exists():
                return ""
            
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                
            # 返回最后N行
            if lines > 0:
                return ''.join(all_lines[-lines:])
            else:
                return ''.join(all_lines)
                
        except Exception as e:
            logging.error(f"❌ 读取日志文件失败 {file_name}: {e}")
            return f"读取日志文件失败: {e}"


# 全局日志引擎实例
log_engine = LogEngine(Path(__file__).parent.parent.parent)
