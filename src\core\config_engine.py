"""
配置引擎 - 统一配置管理和热更新
负责加载、管理和保存所有配置文件
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
import threading
from copy import deepcopy


class ConfigEngine:
    """配置引擎 - 统一配置管理"""
    
    def __init__(self, app_root: Path):
        """初始化配置引擎"""
        self.app_root = app_root
        self.config_dir = app_root / 'config'
        self.configs: Dict[str, Dict] = {}
        self.config_files = {
            'app': 'app.json',
            'themes': 'themes.json', 
            'features': 'features.json',
            'i18n': 'i18n.json'
        }
        self.lock = threading.RLock()
        self.change_listeners: List[callable] = []
        
    def initialize(self) -> bool:
        """初始化配置引擎"""
        try:
            logging.info("⚙️ 正在初始化配置引擎...")
            
            # 加载所有配置文件
            for config_name, file_name in self.config_files.items():
                if not self._load_config(config_name, file_name):
                    logging.warning(f"⚠️ 配置文件加载失败: {file_name}")
            
            logging.info("✅ 配置引擎初始化完成")
            return True
            
        except Exception as e:
            logging.error(f"❌ 配置引擎初始化失败: {e}")
            return False
    
    def _load_config(self, config_name: str, file_name: str) -> bool:
        """加载单个配置文件"""
        try:
            config_path = self.config_dir / file_name
            if not config_path.exists():
                logging.warning(f"⚠️ 配置文件不存在: {config_path}")
                self.configs[config_name] = {}
                return False
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                
            with self.lock:
                self.configs[config_name] = config_data
                
            logging.debug(f"✅ 配置文件加载成功: {file_name}")
            return True
            
        except Exception as e:
            logging.error(f"❌ 配置文件加载失败 {file_name}: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键，如 'app.name' 或 'ui.window.width'
            default: 默认值
            
        Returns:
            配置值或默认值
        """
        try:
            with self.lock:
                # 解析键路径
                parts = key.split('.')
                if len(parts) < 2:
                    return default
                
                config_name = parts[0]
                if config_name not in self.configs:
                    return default
                
                # 遍历嵌套字典
                current = self.configs[config_name]
                for part in parts[1:]:
                    if isinstance(current, dict) and part in current:
                        current = current[part]
                    else:
                        return default
                
                return current
                
        except Exception as e:
            logging.error(f"❌ 获取配置失败 {key}: {e}")
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """设置配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键
            value: 配置值
            
        Returns:
            是否设置成功
        """
        try:
            with self.lock:
                # 解析键路径
                parts = key.split('.')
                if len(parts) < 2:
                    return False
                
                config_name = parts[0]
                if config_name not in self.configs:
                    self.configs[config_name] = {}
                
                # 创建嵌套字典路径
                current = self.configs[config_name]
                for part in parts[1:-1]:
                    if part not in current:
                        current[part] = {}
                    current = current[part]
                
                # 设置值
                old_value = current.get(parts[-1])
                current[parts[-1]] = value
                
                # 通知变更监听器
                self._notify_change(key, old_value, value)
                
                return True
                
        except Exception as e:
            logging.error(f"❌ 设置配置失败 {key}: {e}")
            return False
    
    def get_config(self, config_name: str) -> Dict[str, Any]:
        """获取整个配置文件的内容"""
        with self.lock:
            return deepcopy(self.configs.get(config_name, {}))
    
    def set_config(self, config_name: str, config_data: Dict[str, Any]) -> bool:
        """设置整个配置文件的内容"""
        try:
            with self.lock:
                old_config = self.configs.get(config_name, {})
                self.configs[config_name] = deepcopy(config_data)
                
                # 通知变更监听器
                self._notify_change(f"{config_name}.*", old_config, config_data)
                
                return True
                
        except Exception as e:
            logging.error(f"❌ 设置配置失败 {config_name}: {e}")
            return False
    
    def save(self, config_name: Optional[str] = None) -> bool:
        """保存配置到文件
        
        Args:
            config_name: 配置名称，如果为None则保存所有配置
            
        Returns:
            是否保存成功
        """
        try:
            if config_name:
                return self._save_config(config_name)
            else:
                # 保存所有配置
                success = True
                for name in self.configs.keys():
                    if not self._save_config(name):
                        success = False
                return success
                
        except Exception as e:
            logging.error(f"❌ 保存配置失败: {e}")
            return False
    
    def _save_config(self, config_name: str) -> bool:
        """保存单个配置文件"""
        try:
            if config_name not in self.config_files:
                return False
                
            file_name = self.config_files[config_name]
            config_path = self.config_dir / file_name
            
            with self.lock:
                config_data = self.configs.get(config_name, {})
            
            # 确保目录存在
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存文件
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            logging.debug(f"✅ 配置文件保存成功: {file_name}")
            return True
            
        except Exception as e:
            logging.error(f"❌ 配置文件保存失败 {config_name}: {e}")
            return False
    
    def reload(self, config_name: Optional[str] = None) -> bool:
        """重新加载配置
        
        Args:
            config_name: 配置名称，如果为None则重新加载所有配置
            
        Returns:
            是否重新加载成功
        """
        try:
            if config_name:
                if config_name in self.config_files:
                    return self._load_config(config_name, self.config_files[config_name])
                return False
            else:
                # 重新加载所有配置
                success = True
                for name, file_name in self.config_files.items():
                    if not self._load_config(name, file_name):
                        success = False
                return success
                
        except Exception as e:
            logging.error(f"❌ 重新加载配置失败: {e}")
            return False
    
    def add_change_listener(self, listener: callable) -> None:
        """添加配置变更监听器"""
        if listener not in self.change_listeners:
            self.change_listeners.append(listener)
    
    def remove_change_listener(self, listener: callable) -> None:
        """移除配置变更监听器"""
        if listener in self.change_listeners:
            self.change_listeners.remove(listener)
    
    def _notify_change(self, key: str, old_value: Any, new_value: Any) -> None:
        """通知配置变更"""
        try:
            for listener in self.change_listeners:
                listener(key, old_value, new_value)
        except Exception as e:
            logging.error(f"❌ 通知配置变更失败: {e}")
    
    def is_feature_enabled(self, feature_name: str) -> bool:
        """检查功能是否启用"""
        return self.get(f'features.features.{feature_name}.enabled', False)
    
    def get_theme(self, theme_name: Optional[str] = None) -> Dict[str, Any]:
        """获取主题配置"""
        if not theme_name:
            theme_name = self.get('ui.default_theme', 'modern_blue')
        return self.get(f'themes.themes.{theme_name}', {})
