{"features": {"core_features": {"browser_management": {"enabled": true, "description": "浏览器实例管理功能", "dependencies": []}, "chrome_portable": {"enabled": true, "description": "Chrome Portable集成", "dependencies": ["browser_management"]}, "relative_paths": {"enabled": true, "description": "相对路径绑定技术", "dependencies": ["chrome_portable"]}}, "advanced_features": {"plugin_sync": {"enabled": true, "description": "插件同步功能", "dependencies": ["browser_management"]}, "icon_management": {"enabled": true, "description": "图标管理和下载", "dependencies": []}, "desktop_shortcuts": {"enabled": true, "description": "桌面快捷方式创建", "dependencies": ["icon_management"]}, "auto_update": {"enabled": true, "description": "自动更新功能", "dependencies": []}}, "ui_features": {"theme_switching": {"enabled": true, "description": "主题切换功能", "dependencies": []}, "multi_language": {"enabled": true, "description": "多语言支持", "dependencies": []}, "modern_ui": {"enabled": true, "description": "现代化界面", "dependencies": ["theme_switching"]}, "dark_mode": {"enabled": true, "description": "深色模式", "dependencies": ["theme_switching"]}}, "experimental_features": {"browser_fingerprint": {"enabled": false, "description": "浏览器指纹保护", "dependencies": ["browser_management"]}, "cloud_sync": {"enabled": false, "description": "云配置同步", "dependencies": []}, "ai_assistant": {"enabled": false, "description": "AI智能助手", "dependencies": []}, "team_collaboration": {"enabled": false, "description": "团队协作功能", "dependencies": ["cloud_sync"]}}, "security_features": {"data_encryption": {"enabled": false, "description": "数据加密", "dependencies": []}, "secure_delete": {"enabled": true, "description": "安全删除", "dependencies": []}, "permission_check": {"enabled": true, "description": "权限检查", "dependencies": []}}, "performance_features": {"lazy_loading": {"enabled": true, "description": "延迟加载", "dependencies": []}, "cache_optimization": {"enabled": true, "description": "缓存优化", "dependencies": []}, "memory_management": {"enabled": true, "description": "内存管理", "dependencies": []}}}, "feature_groups": {"essential": ["browser_management", "chrome_portable", "relative_paths"], "recommended": ["plugin_sync", "icon_management", "desktop_shortcuts", "theme_switching"], "optional": ["auto_update", "multi_language", "modern_ui", "dark_mode"], "experimental": ["browser_fingerprint", "cloud_sync", "ai_assistant", "team_collaboration"]}, "environment_overrides": {"development": {"browser_fingerprint": true, "cloud_sync": false, "ai_assistant": false}, "testing": {"auto_update": false, "data_encryption": false}, "production": {"browser_fingerprint": false, "cloud_sync": false, "ai_assistant": false}}}