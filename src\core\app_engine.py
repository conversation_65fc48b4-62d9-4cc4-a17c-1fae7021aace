"""
应用引擎 - 统一的应用生命周期管理
负责应用的启动、初始化、运行和关闭
"""

import sys
import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import traceback

from .config_engine import ConfigEngine
from .event_engine import EventEngine
from .log_engine import LogEngine


class AppEngine:
    """应用引擎 - 应用生命周期管理"""
    
    def __init__(self, app_root: Optional[Path] = None):
        """初始化应用引擎"""
        self.app_root = app_root or Path(__file__).parent.parent.parent
        self.config_engine: Optional[ConfigEngine] = None
        self.event_engine: Optional[EventEngine] = None
        self.log_engine: Optional[LogEngine] = None
        self.is_initialized = False
        self.is_running = False
        
    def initialize(self) -> bool:
        """初始化应用"""
        try:
            print("🚀 正在初始化应用引擎...")
            
            # 1. 初始化日志引擎
            self.log_engine = LogEngine(self.app_root)
            self.log_engine.initialize()
            
            # 2. 初始化配置引擎
            self.config_engine = ConfigEngine(self.app_root)
            self.config_engine.initialize()
            
            # 3. 初始化事件引擎
            self.event_engine = EventEngine()
            self.event_engine.initialize()
            
            # 4. 检查环境
            if not self._check_environment():
                return False
                
            # 5. 创建必要目录
            self._create_directories()
            
            # 6. 发布初始化完成事件
            self.event_engine.publish('app.initialized', {
                'app_root': str(self.app_root),
                'version': self.get_version()
            })
            
            self.is_initialized = True
            logging.info("✅ 应用引擎初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 应用引擎初始化失败: {e}")
            traceback.print_exc()
            return False
    
    def start(self) -> bool:
        """启动应用"""
        try:
            if not self.is_initialized:
                if not self.initialize():
                    return False
            
            logging.info("🚀 正在启动应用...")
            
            # 发布应用启动事件
            self.event_engine.publish('app.starting', {})
            
            self.is_running = True
            
            # 发布应用启动完成事件
            self.event_engine.publish('app.started', {})
            
            logging.info("✅ 应用启动完成")
            return True
            
        except Exception as e:
            logging.error(f"❌ 应用启动失败: {e}")
            return False
    
    def stop(self) -> bool:
        """停止应用"""
        try:
            if not self.is_running:
                return True
                
            logging.info("🛑 正在停止应用...")
            
            # 发布应用停止事件
            self.event_engine.publish('app.stopping', {})
            
            self.is_running = False
            
            # 清理资源
            self._cleanup()
            
            # 发布应用停止完成事件
            self.event_engine.publish('app.stopped', {})
            
            logging.info("✅ 应用停止完成")
            return True
            
        except Exception as e:
            logging.error(f"❌ 应用停止失败: {e}")
            return False
    
    def restart(self) -> bool:
        """重启应用"""
        logging.info("🔄 正在重启应用...")
        if self.stop():
            return self.start()
        return False
    
    def get_version(self) -> str:
        """获取应用版本"""
        if self.config_engine:
            return self.config_engine.get('app.version', 'v2.2.1-重构版')
        return 'v2.2.1-重构版'
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置"""
        if self.config_engine:
            return self.config_engine.get(key, default)
        return default
    
    def set_config(self, key: str, value: Any) -> bool:
        """设置配置"""
        if self.config_engine:
            return self.config_engine.set(key, value)
        return False
    
    def _check_environment(self) -> bool:
        """检查运行环境"""
        try:
            # 检查Python版本
            if sys.version_info < (3, 7):
                logging.error("❌ Python版本过低，需要3.7+")
                return False
            
            # 检查必要文件
            required_files = [
                'config/app.json',
                'config/themes.json',
                'config/features.json'
            ]
            
            for file_path in required_files:
                full_path = self.app_root / file_path
                if not full_path.exists():
                    logging.error(f"❌ 缺少必要文件: {file_path}")
                    return False
            
            # 检查Chrome Portable
            chrome_path = self.app_root / self.get_config('chrome.portable_path', 'GoogleChromePortable/GoogleChromePortable.exe')
            if not chrome_path.exists():
                logging.warning(f"⚠️ Chrome Portable未找到: {chrome_path}")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 环境检查失败: {e}")
            return False
    
    def _create_directories(self) -> None:
        """创建必要目录"""
        directories = [
            'data/browsers',
            'data/icons', 
            'data/plugins',
            'data/logs',
            'temp',
            'backup'
        ]
        
        for directory in directories:
            dir_path = self.app_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def _cleanup(self) -> None:
        """清理资源"""
        try:
            # 清理临时文件
            temp_dir = self.app_root / 'temp'
            if temp_dir.exists():
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
                temp_dir.mkdir(exist_ok=True)
            
            # 保存配置
            if self.config_engine:
                self.config_engine.save()
                
        except Exception as e:
            logging.error(f"❌ 资源清理失败: {e}")


# 全局应用引擎实例
app_engine = AppEngine()
