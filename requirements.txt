# 浏览器多账号绿色版 v2.2.1-重构版
# Python依赖包列表

# 核心依赖
# Python标准库模块（无需安装）
# - tkinter (GUI框架)
# - json (配置管理)
# - logging (日志系统)
# - threading (多线程)
# - pathlib (路径处理)

# Windows系统集成（可选）
pywin32>=306
# 用于创建Windows快捷方式和系统集成

# 网络请求（可选）
requests>=2.25.0
# 用于图标下载、更新检查等网络功能

# 图像处理（可选）
Pillow>=8.0.0
# 用于图标格式转换和处理

# 开发和测试依赖（可选）
pytest>=6.0.0
# 单元测试框架

pytest-cov>=2.10.0
# 测试覆盖率

# 代码质量工具（可选）
flake8>=3.8.0
# 代码风格检查

black>=21.0.0
# 代码格式化

# 文档生成（可选）
sphinx>=3.0.0
# 文档生成工具

# 注意事项:
# 1. 核心功能只依赖Python标准库，确保最大兼容性
# 2. 可选依赖提供增强功能，缺失时会优雅降级
# 3. Windows特定功能需要pywin32
# 4. 网络功能需要requests
# 5. 图标处理需要Pillow

# 安装命令:
# pip install -r requirements.txt

# 最小安装（仅核心功能）:
# 无需安装额外包，使用Python标准库即可

# 推荐安装（完整功能）:
# pip install pywin32 requests Pillow

# 开发环境安装:
# pip install -r requirements.txt
