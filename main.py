#!/usr/bin/env python3
"""
浏览器多账号绿色版 v2.2.1-重构版
主启动文件 - 应用程序入口点

基于CogniGraph™认知图迹系统的现代化架构重构版本
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

import logging
import traceback
from src.core.app_engine import app_engine


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: Python {sys.version}")
        return False
    return True


def check_dependencies():
    """检查必要依赖"""
    try:
        # 检查tkinter
        import tkinter
        print("✅ tkinter 可用")
        
        # 检查其他可选依赖
        optional_deps = []
        
        try:
            import win32com.client
            optional_deps.append("pywin32")
        except ImportError:
            print("⚠️ pywin32 未安装，快捷方式功能可能受限")
        
        try:
            import requests
            optional_deps.append("requests")
        except ImportError:
            print("⚠️ requests 未安装，网络功能可能受限")
        
        if optional_deps:
            print(f"✅ 可选依赖: {', '.join(optional_deps)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 缺少必要依赖: {e}")
        print("请安装必要依赖:")
        print("pip install tkinter")
        return False


def show_startup_info():
    """显示启动信息"""
    print("=" * 60)
    print("🌟 浏览器多账号绿色版 v2.2.1-重构版")
    print("=" * 60)
    print("🧠 基于CogniGraph™认知图迹系统")
    print("🏗️ 现代化微服务架构")
    print("⚡ 配置驱动 + 事件驱动")
    print("🎨 支持主题切换和多语言")
    print("=" * 60)


def main():
    """主函数"""
    try:
        # 显示启动信息
        show_startup_info()
        
        # 检查Python版本
        if not check_python_version():
            input("按回车键退出...")
            return 1
        
        # 检查依赖
        if not check_dependencies():
            input("按回车键退出...")
            return 1
        
        print("\n🚀 正在启动应用...")
        
        # 初始化并启动应用引擎
        if not app_engine.initialize():
            print("❌ 应用引擎初始化失败")
            input("按回车键退出...")
            return 1
        
        if not app_engine.start():
            print("❌ 应用启动失败")
            input("按回车键退出...")
            return 1
        
        print("✅ 应用启动成功")
        
        # 检查是否有图形界面
        try:
            # 尝试启动图形界面
            from src.ui.controllers.main_controller import MainController
            
            print("🖥️ 正在启动图形界面...")
            main_controller = MainController()
            main_controller.run()
            
        except ImportError:
            print("⚠️ 图形界面模块未找到，启动命令行模式")
            # 启动命令行模式
            run_cli_mode()
        
        except Exception as e:
            print(f"❌ 图形界面启动失败: {e}")
            print("🔄 切换到命令行模式...")
            run_cli_mode()
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断程序")
        return 0
        
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        traceback.print_exc()
        input("按回车键退出...")
        return 1
        
    finally:
        # 停止应用引擎
        try:
            if app_engine.is_running:
                print("\n🛑 正在停止应用...")
                app_engine.stop()
                print("✅ 应用已停止")
        except Exception as e:
            print(f"⚠️ 停止应用时出错: {e}")


def run_cli_mode():
    """运行命令行模式"""
    print("\n" + "=" * 50)
    print("⌨️  命令行模式")
    print("=" * 50)
    
    while True:
        try:
            print("\n可用命令:")
            print("1. 查看浏览器列表")
            print("2. 创建浏览器")
            print("3. 启动浏览器")
            print("4. 删除浏览器")
            print("5. 查看配置")
            print("6. 查看日志")
            print("0. 退出")
            
            choice = input("\n请选择操作 (0-6): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                show_browser_list()
            elif choice == '2':
                create_browser()
            elif choice == '3':
                start_browser()
            elif choice == '4':
                delete_browser()
            elif choice == '5':
                show_config()
            elif choice == '6':
                show_logs()
            else:
                print("❌ 无效选择，请重试")
                
        except KeyboardInterrupt:
            print("\n\n⚠️ 用户中断")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")


def show_browser_list():
    """显示浏览器列表"""
    print("\n📁 浏览器列表:")
    browsers_dir = Path("data/browsers")
    if browsers_dir.exists():
        browsers = [d for d in browsers_dir.iterdir() if d.is_dir()]
        if browsers:
            for i, browser in enumerate(browsers, 1):
                print(f"{i}. {browser.name}")
        else:
            print("暂无浏览器实例")
    else:
        print("浏览器目录不存在")


def create_browser():
    """创建浏览器"""
    name = input("请输入浏览器名称: ").strip()
    if name:
        print(f"🆕 创建浏览器: {name}")
        # TODO: 实现浏览器创建逻辑
        print("⚠️ 功能开发中...")
    else:
        print("❌ 名称不能为空")


def start_browser():
    """启动浏览器"""
    name = input("请输入要启动的浏览器名称: ").strip()
    if name:
        print(f"🚀 启动浏览器: {name}")
        # TODO: 实现浏览器启动逻辑
        print("⚠️ 功能开发中...")
    else:
        print("❌ 名称不能为空")


def delete_browser():
    """删除浏览器"""
    name = input("请输入要删除的浏览器名称: ").strip()
    if name:
        confirm = input(f"确认删除浏览器 '{name}' 吗? (y/N): ").strip().lower()
        if confirm == 'y':
            print(f"🗑️ 删除浏览器: {name}")
            # TODO: 实现浏览器删除逻辑
            print("⚠️ 功能开发中...")
        else:
            print("❌ 操作已取消")
    else:
        print("❌ 名称不能为空")


def show_config():
    """显示配置信息"""
    print("\n⚙️ 配置信息:")
    print(f"应用版本: {app_engine.get_version()}")
    print(f"应用根目录: {app_engine.app_root}")
    print(f"配置目录: {app_engine.app_root / 'config'}")
    print(f"数据目录: {app_engine.app_root / 'data'}")


def show_logs():
    """显示日志信息"""
    print("\n📝 最近日志:")
    try:
        log_files = app_engine.log_engine.get_log_files()
        if log_files:
            latest_log = log_files[0]
            print(f"最新日志文件: {latest_log['name']}")
            print(f"文件大小: {latest_log['size']} 字节")
            print(f"修改时间: {latest_log['modified']}")
            
            # 显示最后10行
            content = app_engine.log_engine.read_log_file(latest_log['name'], 10)
            if content:
                print("\n最后10行:")
                print("-" * 50)
                print(content)
                print("-" * 50)
        else:
            print("暂无日志文件")
    except Exception as e:
        print(f"❌ 读取日志失败: {e}")


if __name__ == "__main__":
    sys.exit(main())
